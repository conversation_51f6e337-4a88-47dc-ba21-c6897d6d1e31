{"name": "@rocketsourcing/extension", "version": "3.4.1", "private": true, "type": "module", "scripts": {"clean": "if exist dist rmdir /s /q dist", "dev": "pnpm clean && pnpm dev:1688", "dev:1688": "cross-env VITE_ENTRY=1688 vite build --watch --mode development", "dev:bridge": "cross-env VITE_ENTRY=bridge vite build --watch --mode development", "dev:coupang": "cross-env VITE_ENTRY=coupang vite build --watch --mode development", "dev:supplier": "cross-env VITE_ENTRY=supplier vite build --watch --mode development", "dev:worker": "cross-env VITE_ENTRY=worker vite build --watch --mode development", "dev:unix": "rm -rf dist && VITE_ENTRY=1688 vite build --watch --mode development & VITE_ENTRY=bridge vite build --watch --mode development & VITE_ENTRY=coupang vite build --watch --mode development & VITE_ENTRY=supplier vite build --watch --mode development & VITE_ENTRY=worker vite build --watch --mode development & wait", "build": "pnpm clean && pnpm build:1688 && pnpm build:bridge && pnpm build:coupang && pnpm build:supplier && pnpm build:worker", "build:1688": "cross-env VITE_ENTRY=1688 vite build", "build:bridge": "cross-env VITE_ENTRY=bridge vite build", "build:coupang": "cross-env VITE_ENTRY=coupang vite build", "build:supplier": "cross-env VITE_ENTRY=supplier vite build", "build:worker": "cross-env VITE_ENTRY=worker vite build", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/css": "^11.13.5", "@rocketsourcing/shared": "workspace:*", "@vitejs/plugin-react": "^4.6.0", "eventemitter3": "^5.0.1", "jwt-decode": "^4.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "zod": "catalog:", "zustand": "catalog:"}, "devDependencies": {"@types/chrome": "^0.0.326", "@types/node": "catalog:", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "cross-env": "^10.0.0", "rollup-obfuscator": "^4.1.1", "typescript": "catalog:", "vite": "^5.2.0", "vite-plugin-remove-console": "^2.2.0"}}