/* 쿠팡 원가 및 설정 패널 스타일 */
#cost-filter-panel {
  position: fixed;
  bottom: 80px;
  right: 20px;
  padding: 15px;
  border-radius: 10px;
  z-index: 2147483646;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  font-size: 13px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.3);
  background: linear-gradient(135deg, #3a7bd5, #00d2ff);
  color: white;
  text-align: right;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  width: 220px;
  max-height: 500px;
  border: 1px solid rgba(255,255,255,0.2);
  opacity: 0.95;
}

#cost-filter-toggle {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  width: 50px !important;
  height: 50px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg, #3a7bd5, #00d2ff) !important;
  color: white !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 4px 16px rgba(0,0,0,0.4) !important;
  z-index: 2147483647 !important;
  font-size: 18px !important;
  font-weight: bold !important;
  border: 4px solid white !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
  user-select: none !important;
  transition: transform 0.2s ease !important;
}

#cost-filter-toggle:hover {
  transform: scale(1.1) !important;
}

/* 모든 가능한 선택자로 강제 적용 */
div[id="cost-filter-toggle"],
*[id="cost-filter-toggle"] {
  position: fixed !important;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 2147483647 !important;
}

#cost-filter-panel .panel-title {
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 4px;
  text-align: center;
  padding-bottom: 3px;
  border-bottom: 1px solid rgba(255,255,255,0.3);
}

#cost-filter-panel .settings-section {
  margin-bottom: 5px;
  border-bottom: 1px solid rgba(255,255,255,0.2);
  padding-bottom: 3px;
}

#cost-filter-panel .settings-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

#cost-filter-panel .section-title {
  font-weight: bold;
  margin-bottom: 5px;
  font-size: 13px;
  text-align: left;
  color: rgba(255,255,255,0.9);
}

#cost-filter-panel .input-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
}

#cost-filter-panel .input-group:last-child {
  margin-bottom: 0;
}

#cost-filter-panel label {
  color: white;
  flex: 1;
  text-align: left;
  margin-right: 3px;
}

#cost-filter-panel input[type="number"] {
  background-color: rgba(255,255,255,0.9);
  color: #333;
  border: none;
  border-radius: 4px;
  padding: 4px;
  width: 50px;
  text-align: right;
  font-weight: bold;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

#cost-filter-panel input[type="number"]:focus {
  outline: none;
  background-color: white;
  box-shadow: 0 0 0 2px rgba(255,255,255,0.5);
}

#cost-filter-panel .toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 20px;
}

#cost-filter-panel .toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

#cost-filter-panel .toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255,255,255,0.3);
  transition: .4s;
  border-radius: 20px;
}

#cost-filter-panel .toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

#cost-filter-panel input:checked + .toggle-slider {
  background-color: #4ecca3;
}

#cost-filter-panel input:checked + .toggle-slider:before {
  transform: translateX(30px);
}

#cost-filter-panel .unit {
  margin-left: 2px;
  font-size: 11px;
  color: rgba(255,255,255,0.8);
}

.recommended-cost {
  font-size: 12px;
  font-weight: bold;
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  background: linear-gradient(135deg, #3a7bd5, #00d2ff);
  color: white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  margin-bottom: 4px;
  animation: fadeIn 0.3s ease-in;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  pointer-events: auto;
}

/* 상품 전체 호버 영역 확장 */
li.ProductUnit_productUnit__Qd6sv,
li[class*='ProductUnit_productUnit'],
li[class*='productUnit'] {
  cursor: pointer;
  transition: all 0.2s ease;
}

li.ProductUnit_productUnit__Qd6sv:hover,
li[class*='ProductUnit_productUnit']:hover,
li[class*='productUnit']:hover {
  background-color: rgba(58, 123, 213, 0.02);
}

li.ProductUnit_productUnit__Qd6sv:hover .recommended-cost,
li[class*='ProductUnit_productUnit']:hover .recommended-cost,
li[class*='productUnit']:hover .recommended-cost {
  background: linear-gradient(135deg, #4ecca3, #44d362) !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 12px rgba(76, 204, 163, 0.4) !important;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.filtered-product {
  filter: grayscale(100%) opacity(0.7);
  transition: all 0.3s ease;
}

/* Aliprice 관련 스타일 */
.chinese-cost-display { }
.aliprice-filtered-out { }

/* Aliprice 하이라이트 박스 (기준 가격 이하) - 모던한 디자인 */
.highlight-box {
  background: linear-gradient(135deg, rgba(58, 123, 213, 0.1), rgba(0, 210, 255, 0.1)) !important;
  border: 2px solid transparent !important;
  border-image: linear-gradient(135deg, #3a7bd5, #00d2ff) 1 !important;
  border-radius: 12px !important;
  padding: 15px !important;
  margin: 8px !important;
  box-shadow:
      0 8px 32px rgba(58, 123, 213, 0.15),
      0 2px 8px rgba(0, 210, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  position: relative !important;
  overflow: hidden !important;
  transform: translateY(-2px) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  filter: none !important;
}

.highlight-box::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: linear-gradient(135deg, rgba(58, 123, 213, 0.05), rgba(0, 210, 255, 0.05)) !important;
  border-radius: 10px !important;
  z-index: -1 !important;
}

.highlight-box::after {
  content: '✓ 기준가격 이하' !important;
  position: absolute !important;
  top: 8px !important;
  right: 8px !important;
  background: linear-gradient(135deg, #3a7bd5, #00d2ff) !important;
  color: white !important;
  padding: 4px 8px !important;
  border-radius: 12px !important;
  font-size: 10px !important;
  font-weight: bold !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  z-index: 10 !important;
}

.highlight-box:hover {
  transform: translateY(-4px) !important;
  box-shadow:
      0 12px 40px rgba(58, 123, 213, 0.2),
      0 4px 12px rgba(0, 210, 255, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.non-highlight-box {
  background: rgba(128, 128, 128, 0.05) !important;
  border: 1px solid rgba(128, 128, 128, 0.2) !important;
  border-radius: 8px !important;
  filter: grayscale(30%) opacity(0.7) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
}

.non-highlight-box::after {
  content: '기준가격 초과' !important;
  position: absolute !important;
  top: 8px !important;
  right: 8px !important;
  background: rgba(128, 128, 128, 0.8) !important;
  color: white !important;
  padding: 4px 8px !important;
  border-radius: 12px !important;
  font-size: 10px !important;
  font-weight: bold !important;
  z-index: 10 !important;
}