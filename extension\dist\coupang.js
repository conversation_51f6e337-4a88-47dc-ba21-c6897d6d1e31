var D=Object.defineProperty,N=Object.defineProperties;var U=Object.getOwnPropertyDescriptors;var k=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,H=Object.prototype.propertyIsEnumerable;var F=(c,t,e)=>t in c?D(c,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):c[t]=e,y=(c,t)=>{for(var e in t||(t={}))$.call(t,e)&&F(c,e,t[e]);if(k)for(var e of k(t))H.call(t,e)&&F(c,e,t[e]);return c},S=(c,t)=>N(c,U(t));var f=(c,t,e)=>F(c,typeof t!="symbol"?t+"":t,e);const v=class v{async loadSettings(t=!1){try{if(typeof chrome!="undefined"&&chrome.storage)return new Promise(e=>{chrome.storage.local.get([v.STORAGE_KEY],i=>{const n=i[v.STORAGE_KEY]||{};e(this.applyUrlChangeReset(n,t))})});{const e=sessionStorage.getItem(v.STORAGE_KEY),i=e?JSON.parse(e):{};return this.applyUrlChangeReset(i,t)}}catch(e){return console.warn("Failed to load settings:",e),v.DEFAULT_SETTINGS}}async saveSettings(t){try{if(typeof chrome!="undefined"&&chrome.storage)return new Promise(e=>{chrome.storage.local.set({[v.STORAGE_KEY]:t},()=>{e()})});sessionStorage.setItem(v.STORAGE_KEY,JSON.stringify(t))}catch(e){console.warn("Failed to save settings:",e)}}applyUrlChangeReset(t,e){const i=y(y({},v.DEFAULT_SETTINGS),t);return e&&(i.기준가격=20,i.lastHoveredCost=null,i.lastHoveredProductId=null),i}validatePageChange(t){return this.getCurrentPageProductId()&&t.lastHoveredProductId&&!window.location.href.includes(t.lastHoveredProductId)?S(y({},t),{기준가격:20,lastHoveredCost:null,lastHoveredProductId:null}):t}getCurrentPageProductId(){const t=window.location.href.match(/\/vp\/products\/(\d+)/);return t?t[1]:(window.location.href.includes("/np/search")||window.location.href.includes("/np/categories"),null)}};f(v,"STORAGE_KEY","coupangCostCalculator"),f(v,"DEFAULT_SETTINGS",{coupangMarginRate:.4,supplyMarginRate:.4,exchangeRate:320,minRatingFilter:4,maxPriceFilter:1e5,minReviewsFilter:50,excludeRocketDelivery:!1,isFilterEnabled:!0,alipriceCostFilterEnabled:!0,기준가격:20,lastHoveredCost:null,lastHoveredProductId:null,minRatingFilterEnabled:!0,maxPriceFilterEnabled:!0,minReviewsFilterEnabled:!0});let T=v;class L{static calculateRecommendedChineseCost(t,e,i,n){const l=parseFloat(t.replace(/,/g,"").replace("원","").trim());if(isNaN(l))return"계산 불가";const m=Math.min(Math.max(e,0),.99),p=Math.min(Math.max(i,0),.99),o=Math.max(n,1);return(l*(1-m)*(1-p)/o).toFixed(2)}static parsePrice(t){const e=t.match(/(¥|엔)([\d.]+)/);return e?parseFloat(e[2]):null}static validateAndAdjustBasePrice(t,e,i,n,l){if(t.length===0)return e;let m=0,p=0;if(t.forEach(o=>{var a;const r=o.querySelector(".Price_priceValue__A4KOr, .price-value, [class*='Price_priceValue'], [class*='priceValue']");if(r){const s=((a=r.textContent)==null?void 0:a.trim())||"",g=this.calculateRecommendedChineseCost(s,i,n,l),u=parseFloat(g);!isNaN(u)&&u>0&&(m+=u,p++)}}),p>0){const o=m/p;if(e<o*.1||e>o*5)return Math.round(o*100)/100}return e}}class G{static shouldFilterProduct(t,e,i){if(!i.enabled)return{shouldFilter:!1,reason:""};if(i.minRatingEnabled){const n=this.extractRating(t);if(n<i.minRating&&i.minRating>0)return{shouldFilter:!0,reason:`Rating ${n} < ${i.minRating}`}}if(i.maxPriceEnabled){const n=parseFloat(e.replace(/,/g,"").replace("원","").trim())||0;if(n>=i.maxPrice&&i.maxPrice>0)return{shouldFilter:!0,reason:`Price ${n} >= ${i.maxPrice}`}}if(i.excludeRocket&&this.hasRocketDelivery(t))return{shouldFilter:!0,reason:"Rocket delivery excluded (seller rocket allowed)"};if(i.minReviewsEnabled){const n=this.extractReviewCount(t);if(n<i.minReviews&&i.minReviews>0)return{shouldFilter:!0,reason:`Review count ${n} < ${i.minReviews}`}}return{shouldFilter:!1,reason:""}}static extractRating(t){const e=t.querySelector(".ProductRating_star__RGSlV, .star em.rating, [class*='ProductRating_star'], [class*='rating']");if(!e)return 5;const i=e.style.width;return i?parseFloat(i.replace("%",""))/20:5}static hasRocketDelivery(t){const e=t.querySelector('img[alt*="로켓"]');if(e){const i=e.src;return i.includes("logo_rocket_large")&&!i.includes("logoRocketMerchant")}return!1}static extractReviewCount(t){var n,l;const e=t.querySelector(".ProductRating_ratingCount__R0Vhz, .rating-total-count, [class*='ProductRating_ratingCount'], [class*='ratingCount']");if(!e)return 999;const i=((n=e.textContent)==null?void 0:n.match(/\((\d+)\)/))||((l=e.textContent)==null?void 0:l.match(/(\d+)/));return i&&i[1]?parseInt(i[1]):999}}class E{static updateCostElement(t,e){this.addCostElementStyles();let i=t.querySelector(`.${this.COST_ELEMENT_CLASS}`);const n=`추천중국 원가: ${e} CNY`;i&&i.textContent===n||!i&&(i=this.createCostElement(t),!i)||(i.textContent=n,i.setAttribute("title","이 상품에 마우스를 올리면 해당 원가가 Aliprice 기준가격으로 자동 설정됩니다"))}static removeCostElement(t){const e=t.querySelector(`.${this.COST_ELEMENT_CLASS}`);e==null||e.remove()}static markAsFiltered(t){t.classList.add(this.FILTERED_PRODUCT_CLASS),this.removeCostElement(t)}static unmarkAsFiltered(t){t.classList.remove(this.FILTERED_PRODUCT_CLASS)}static createControlPanel(t,e){this.removeExistingElements();const i=this.createPanelElement(),n=this.createToggleButton(i);this.buildPanelContent(i,t,e),document.body.appendChild(i),document.body.insertAdjacentHTML("beforeend",n),this.setupToggleEvents(i),this.startToggleProtection()}static createCostElement(t){const e=document.createElement("div");e.className=this.COST_ELEMENT_CLASS;const i=this.findInsertLocation(t);return i?(i.tagName==="FIGURE"?i.insertAdjacentElement("afterend",e):i.appendChild(e),e):null}static findInsertLocation(t){const e=t.querySelector('.ProductUnit_productImage__Mqcg1, figure[class*="productImage"]');if(e&&e.nextElementSibling)return e;const i=['.ProductUnit_productInfo__1l0il, .descriptions, [class*="productInfo"]','.PriceArea_priceArea__NntJz, .price-area, [class*="PriceArea"], [class*="priceArea"]',".Price_priceValue__A4KOr, .price-value"];for(const n of i){const l=t.querySelector(n);if(l)return n.includes("Price_priceValue")?l.parentNode:l}return null}static removeExistingElements(){document.querySelectorAll(`#${this.PANEL_ID}, #${this.TOGGLE_ID}, .cost-toggle-btn`).forEach(t=>t.remove())}static createPanelElement(){const t=document.createElement("div");return t.id=this.PANEL_ID,t.style.display="none",t}static createToggleButton(t){return`
      <div id="${this.TOGGLE_ID}" style="
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        width: 50px !important;
        height: 50px !important;
        border-radius: 50% !important;
        background: linear-gradient(135deg, #3a7bd5, #00d2ff) !important;
        color: white !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-shadow: 0 4px 16px rgba(0,0,0,0.4) !important;
        z-index: 999999999 !important;
        font-size: 18px !important;
        font-weight: bold !important;
        border: 4px solid white !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
        user-select: none !important;
      ">⚙</div>
    `}static formatNumberWithCommas(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}static parseNumberWithCommas(t){return parseFloat(t.replace(/,/g,""))||0}static buildPanelContent(t,e,i){t.innerHTML=`
      <style>
        #${this.PANEL_ID} {
          position: fixed !important;
          bottom: 80px !important;
          right: 20px !important;
          width: 240px !important;
          background: linear-gradient(135deg, #3a7bd5, #00d2ff) !important;
          border-radius: 10px !important;
          padding: 15px !important;
          box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
          z-index: 999999998 !important;
          color: white !important;
          font-family: 'Helvetica Neue', Arial, sans-serif !important;
          font-size: 13px !important;
          text-align: right !important;
          transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
          max-height: 500px !important;
          border: 1px solid rgba(255,255,255,0.2) !important;
          opacity: 0.95 !important;
        }
        .panel-title {
          font-size: 15px !important;
          font-weight: bold !important;
          margin-bottom: 4px !important;
          text-align: center !important;
          padding-bottom: 3px !important;
          border-bottom: 1px solid rgba(255,255,255,0.3) !important;
        }
        .settings-section {
          margin-bottom: 5px !important;
          border-bottom: 1px solid rgba(255,255,255,0.2) !important;
          padding-bottom: 3px !important;
        }
        .settings-section:last-child {
          border-bottom: none !important;
          margin-bottom: 0 !important;
        }
        .section-title {
          font-size: 13px !important;
          font-weight: bold !important;
          margin-bottom: 5px !important;
          text-align: left !important;
          color: rgba(255,255,255,0.9) !important;
        }
        .input-group {
          display: flex !important;
          justify-content: space-between !important;
          align-items: center !important;
          margin-bottom: 3px !important;
        }
        .input-group:last-child {
          margin-bottom: 0 !important;
        }
        .input-group.toggle-group {
          justify-content: flex-end !important;
          gap: 8px !important;
        }
        .input-group label {
          color: white !important;
          flex: 1 !important;
          text-align: left !important;
          margin-right: 3px !important;
          font-size: 13px !important;
        }
        .input-group input[type="number"],
        .input-group input[type="text"] {
          width: 70px !important;
          padding: 4px !important;
          border: none !important;
          border-radius: 4px !important;
          background-color: rgba(255,255,255,0.9) !important;
          color: #333 !important;
          text-align: right !important;
          font-weight: bold !important;
          box-shadow: inset 0 1px 2px rgba(0,0,0,0.1) !important;
          font-size: 13px !important;
        }
        .input-group input[type="number"]:focus,
        .input-group input[type="text"]:focus {
          outline: none !important;
          background-color: white !important;
          box-shadow: 0 0 0 2px rgba(255,255,255,0.5) !important;
        }
        .input-group input[type="number"]:disabled,
        .input-group input[type="text"]:disabled {
          background: rgba(255,255,255,0.3) !important;
          color: #666 !important;
          cursor: not-allowed !important;
        }
        .unit {
          font-size: 11px !important;
          margin-left: 2px !important;
          color: rgba(255,255,255,0.8) !important;
        }
        .toggle-switch {
          position: relative !important;
          display: inline-block !important;
          width: 50px !important;
          height: 20px !important;
        }
        .toggle-switch input {
          opacity: 0 !important;
          width: 0 !important;
          height: 0 !important;
        }
        .toggle-slider {
          position: absolute !important;
          cursor: pointer !important;
          top: 0 !important;
          left: 0 !important;
          right: 0 !important;
          bottom: 0 !important;
          background-color: rgba(255,255,255,0.3) !important;
          transition: .4s !important;
          border-radius: 20px !important;
        }
        .toggle-slider:before {
          position: absolute !important;
          content: "" !important;
          height: 16px !important;
          width: 16px !important;
          left: 2px !important;
          bottom: 2px !important;
          background-color: white !important;
          transition: .4s !important;
          border-radius: 50% !important;
        }
        input:checked + .toggle-slider {
          background-color: #4ecca3 !important;
        }
        input:checked + .toggle-slider:before {
          transform: translateX(30px) !important;
        }
        input[type="checkbox"]:not(.toggle-switch input) {
          width: 16px !important;
          height: 16px !important;
          cursor: pointer !important;
          appearance: none !important;
          background-color: rgba(255,255,255,0.9) !important;
          border: 2px solid #4ecca3 !important;
          border-radius: 3px !important;
          position: relative !important;
        }
        input[type="checkbox"]:not(.toggle-switch input):checked {
          background-color: #4ecca3 !important;
        }
        input[type="checkbox"]:not(.toggle-switch input):checked::after {
          content: "" !important;
          position: absolute !important;
          left: 4px !important;
          top: 0px !important;
          width: 4px !important;
          height: 8px !important;
          border: solid white !important;
          border-width: 0 2px 2px 0 !important;
          transform: rotate(45deg) !important;
        }
      </style>
      <div class="panel-title">추천중국 원가 설정</div>
      
      <div class="settings-section">
        <div class="section-title">마진율 & 환율</div>
        <div class="input-group">
          <label>쿠팡 마진율:</label>
          <input type="number" id="coupangMarginRate" value="${e.coupangMarginRate*100}" step="0.5">
          <span class="unit">%</span>
        </div>
        <div class="input-group">
          <label>공급 마진율:</label>
          <input type="number" id="supplyMarginRate" value="${e.supplyMarginRate*100}" step="0.5">
          <span class="unit">%</span>
        </div>
        <div class="input-group">
          <label>환율:</label>
          <input type="number" id="exchangeRate" value="${e.exchangeRate}">
          <span class="unit">원</span>
        </div>
      </div>

      <div class="settings-section">
        <div class="section-title">쿠팡 필터</div>
        <div class="input-group" style="align-items: center;">
          <input type="checkbox" id="minRatingFilterEnabled" ${e.minRatingFilterEnabled?"checked":""} style="margin-right: 6px;">
          <label style="min-width: 65px;">최소 별점:</label>
          <input type="number" id="minRatingFilter" value="${e.minRatingFilter}" step="0.5" ${e.minRatingFilterEnabled?"":"disabled"}>
          <span class="unit">점</span>
        </div>
        <div class="input-group" style="align-items: center;">
          <input type="checkbox" id="maxPriceFilterEnabled" ${e.maxPriceFilterEnabled?"checked":""} style="margin-right: 6px;">
          <label style="min-width: 65px;">최대 가격:</label>
          <input type="text" id="maxPriceFilter" value="${this.formatNumberWithCommas(e.maxPriceFilter)}" ${e.maxPriceFilterEnabled?"":"disabled"}>
          <span class="unit">원</span>
        </div>
        <div class="input-group" style="align-items: center;">
          <input type="checkbox" id="minReviewsFilterEnabled" ${e.minReviewsFilterEnabled?"checked":""} style="margin-right: 6px;">
          <label style="min-width: 65px;">최소 리뷰:</label>
          <input type="number" id="minReviewsFilter" value="${e.minReviewsFilter}" ${e.minReviewsFilterEnabled?"":"disabled"}>
          <span class="unit">개</span>
        </div>
        <div class="input-group toggle-group">
          <label>로켓배송 제외:</label>
          <label class="toggle-switch">
            <input type="checkbox" id="excludeRocketDelivery" ${e.excludeRocketDelivery?"checked":""}>
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <div class="settings-section">
        <div class="section-title">Aliprice 필터</div>
        <div class="input-group toggle-group">
          <label>원가 이하만 보기:</label>
          <label class="toggle-switch">
            <input type="checkbox" id="alipriceCostFilterEnabled" ${e.alipriceCostFilterEnabled?"checked":""}>
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>
    `,this.setupInputEvents(t,e,i)}static setupInputEvents(t,e,i){[{id:"coupangMarginRate",key:"coupangMarginRate",multiplier:.01},{id:"supplyMarginRate",key:"supplyMarginRate",multiplier:.01},{id:"exchangeRate",key:"exchangeRate",multiplier:1},{id:"minRatingFilter",key:"minRatingFilter",multiplier:1},{id:"minReviewsFilter",key:"minReviewsFilter",multiplier:1}].forEach(({id:p,key:o,multiplier:r})=>{const a=t.querySelector(`#${p}`);a&&a.addEventListener("input",s=>{const g=parseFloat(s.target.value)||0,u=S(y({},e),{[o]:g*r});i(u)})});const l=t.querySelector("#maxPriceFilter");l&&l.addEventListener("input",p=>{const o=p.target,r=o.selectionStart||0,a=o.value.length,s=this.parseNumberWithCommas(o.value),g=this.formatNumberWithCommas(s);o.value=g;const h=g.length-a;o.setSelectionRange&&o.setSelectionRange(r+h,r+h);const x=S(y({},e),{maxPriceFilter:s});i(x)}),[{id:"excludeRocketDelivery",key:"excludeRocketDelivery"},{id:"alipriceCostFilterEnabled",key:"alipriceCostFilterEnabled"},{id:"minRatingFilterEnabled",key:"minRatingFilterEnabled"},{id:"maxPriceFilterEnabled",key:"maxPriceFilterEnabled"},{id:"minReviewsFilterEnabled",key:"minReviewsFilterEnabled"}].forEach(({id:p,key:o})=>{const r=t.querySelector(`#${p}`);r&&r.addEventListener("change",a=>{const s=a.target.checked,g=S(y({},e),{[o]:s});if(p==="minRatingFilterEnabled"){const u=t.querySelector("#minRatingFilter");u&&(u.disabled=!s)}else if(p==="maxPriceFilterEnabled"){const u=t.querySelector("#maxPriceFilter");u&&(u.disabled=!s)}else if(p==="minReviewsFilterEnabled"){const u=t.querySelector("#minReviewsFilter");u&&(u.disabled=!s)}i(g)})})}static setupToggleEvents(t){const e=document.getElementById(this.TOGGLE_ID);if(!e)return;let i=!1;e.onclick=n=>{n.preventDefault(),n.stopPropagation(),i=!i,t.style.display=i?"block":"none",e.innerHTML=i?"✕":"⚙"},e.onmouseenter=()=>{e.style.transform="scale(1.1)"},e.onmouseleave=()=>{e.style.transform="scale(1)"}}static startToggleProtection(){const t=this.createToggleButton(document.createElement("div"));setInterval(()=>{if(!document.getElementById(this.TOGGLE_ID)){document.body.insertAdjacentHTML("beforeend",t);const i=document.getElementById(this.PANEL_ID);i&&this.setupToggleEvents(i)}},2e3)}static addCostElementStyles(){if(document.getElementById("cost-element-styles"))return;const t=document.createElement("style");t.id="cost-element-styles",t.textContent=`
      .${this.COST_ELEMENT_CLASS} {
        background: linear-gradient(135deg, #3a7bd5, #00d2ff) !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 6px !important;
        font-size: 11px !important;
        font-weight: 600 !important;
        margin: 4px auto !important;
        display: block !important;
        width: fit-content !important;
        text-align: center !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      }
      
      .${this.COST_ELEMENT_CLASS}:hover {
        transform: scale(1.05) !important;
        box-shadow: 0 2px 8px rgba(58, 123, 213, 0.3) !important;
      }
      
      .${this.FILTERED_PRODUCT_CLASS} {
        opacity: 0.3 !important;
        filter: grayscale(1) !important;
      }
    `,document.head.appendChild(t)}}f(E,"COST_ELEMENT_CLASS","recommended-cost"),f(E,"FILTERED_PRODUCT_CLASS","filtered-product"),f(E,"PANEL_ID","cost-filter-panel"),f(E,"TOGGLE_ID","cost-filter-toggle");class M{static processProducts(t,e){const i=document.querySelectorAll(this.PRODUCT_SELECTORS.join(", "));i.length>0&&this.validateBasePrice(i,t),i.forEach((n,l)=>{this.processProduct(n,l,t,e)})}static processProduct(t,e,i,n){var a;E.unmarkAsFiltered(t);const l=t.querySelector(".Price_priceValue__A4KOr, .price-value, [class*='Price_priceValue'], [class*='priceValue']");if(!l)return;const m=((a=l.textContent)==null?void 0:a.trim())||"",p=L.calculateRecommendedChineseCost(m,i.coupangMarginRate,i.supplyMarginRate,i.exchangeRate),o={minRating:i.minRatingFilter,maxPrice:i.maxPriceFilter,minReviews:i.minReviewsFilter,excludeRocket:i.excludeRocketDelivery,enabled:i.isFilterEnabled,minRatingEnabled:i.minRatingFilterEnabled,maxPriceEnabled:i.maxPriceFilterEnabled,minReviewsEnabled:i.minReviewsFilterEnabled};G.shouldFilterProduct(t,m,o).shouldFilter?E.markAsFiltered(t):(E.updateCostElement(t,p),this.setupProductEvents(t,p,n))}static setupProductEvents(t,e,i){t.hasAttribute("data-hover-registered")||(t.setAttribute("data-hover-registered","true"),t.addEventListener("mouseenter",()=>{const n=parseFloat(e);if(isNaN(n)||!i)return;const l=this.extractProductId(t);i(n,l),this.highlightCostElement(t,!0)}),t.addEventListener("mouseleave",()=>{this.highlightCostElement(t,!1)}))}static extractProductId(t){const e=t.querySelector('a[href*="/vp/products/"]');if(e){const m=e.href.match(/\/vp\/products\/(\d+)/);if(m)return m[1]}const i=document.querySelectorAll(this.PRODUCT_SELECTORS.join(", "));return`page-product-${Array.from(i).indexOf(t)}`}static highlightCostElement(t,e){const i=t.querySelector(".recommended-cost");i&&(e?(i.style.background="linear-gradient(135deg, #4ecca3, #44d362)",i.style.transform="scale(1.1)",i.style.boxShadow="0 4px 12px rgba(76, 204, 163, 0.4)"):(i.style.background="linear-gradient(135deg, #3a7bd5, #00d2ff)",i.style.transform="scale(1)",i.style.boxShadow="0 1px 3px rgba(0,0,0,0.1)"))}static validateBasePrice(t,e){setTimeout(()=>{const i=L.validateAndAdjustBasePrice(t,e.기준가격,e.coupangMarginRate,e.supplyMarginRate,e.exchangeRate);e.기준가격},500)}}f(M,"PRODUCT_SELECTORS",["li.ProductUnit_productUnit__Qd6sv","li[class*='ProductUnit_productUnit']","li[class*='productUnit']"]);class q{constructor(t){f(this,"currentUrl");f(this,"onUrlChangeCallback");this.currentUrl=window.location.href,this.onUrlChangeCallback=t}startMonitoring(){this.setupIntervalCheck(),this.setupHistoryApiMonitoring(),this.setupPopstateListener()}setupIntervalCheck(){setInterval(()=>{this.checkUrlChange()},500)}setupHistoryApiMonitoring(){const t=history.pushState,e=history.replaceState;history.pushState=(...i)=>{t.apply(history,i),setTimeout(()=>this.checkUrlChange(),100)},history.replaceState=(...i)=>{e.apply(history,i),setTimeout(()=>this.checkUrlChange(),100)}}setupPopstateListener(){window.addEventListener("popstate",()=>{setTimeout(()=>this.checkUrlChange(),100)})}checkUrlChange(){const t=window.location.href;this.currentUrl!==t&&(this.currentUrl=t,this.onUrlChangeCallback())}}class R{static applyStyles(t){if(!t.alipriceCostFilterEnabled){document.querySelectorAll(".ap-list-card").forEach(n=>{n.classList.remove("highlight-box","non-highlight-box")});return}document.querySelectorAll(".ap-list-card").forEach(i=>{var l;const n=i.querySelector(".ap-product-price");if(n){const m=((l=n.textContent)==null?void 0:l.trim())||"",p=this.parsePrice(m);p!==null&&p<=t.기준가격?(i.classList.add("highlight-box"),i.classList.remove("non-highlight-box")):(i.classList.remove("highlight-box"),i.classList.add("non-highlight-box"))}else i.classList.remove("highlight-box","non-highlight-box")})}static parsePrice(t){const e=t.match(/(¥|엔)([\d.]+)/);return e?parseFloat(e[2]):null}static addStyles(){if(document.getElementById("aliprice-filter-styles"))return;const t=document.createElement("style");t.id="aliprice-filter-styles",t.textContent=`
      /* Aliprice 하이라이트 박스 (기준 가격 이하) */
      .highlight-box {
        background: linear-gradient(135deg, rgba(58, 123, 213, 0.1), rgba(0, 210, 255, 0.1)) !important;
        border: 2px solid transparent !important;
        border-image: linear-gradient(135deg, #3a7bd5, #00d2ff) 1 !important;
        border-radius: 12px !important;
        padding: 15px !important;
        margin: 8px !important;
        box-shadow:
          0 8px 32px rgba(58, 123, 213, 0.15),
          0 2px 8px rgba(0, 210, 255, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        position: relative !important;
        overflow: hidden !important;
        transform: translateY(-2px) !important;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
        filter: none !important;
      }

      .highlight-box::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: linear-gradient(135deg, rgba(58, 123, 213, 0.05), rgba(0, 210, 255, 0.05)) !important;
        border-radius: 10px !important;
        z-index: -1 !important;
      }

      .highlight-box::after {
        content: '✓ 기준가격 이하' !important;
        position: absolute !important;
        top: 8px !important;
        right: 8px !important;
        background: linear-gradient(135deg, #3a7bd5, #00d2ff) !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 12px !important;
        font-size: 10px !important;
        font-weight: bold !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        z-index: 10 !important;
      }

      .highlight-box:hover {
        transform: translateY(-4px) !important;
        box-shadow:
          0 12px 40px rgba(58, 123, 213, 0.2),
          0 4px 12px rgba(0, 210, 255, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
      }

      .non-highlight-box {
        background: rgba(128, 128, 128, 0.05) !important;
        border: 1px solid rgba(128, 128, 128, 0.2) !important;
        border-radius: 8px !important;
        filter: grayscale(30%) opacity(0.7) !important;
        transition: all 0.3s ease !important;
        position: relative !important;
      }

      .non-highlight-box::after {
        content: '기준가격 초과' !important;
        position: absolute !important;
        top: 8px !important;
        right: 8px !important;
        background: rgba(128, 128, 128, 0.8) !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 12px !important;
        font-size: 10px !important;
        font-weight: bold !important;
        z-index: 10 !important;
      }
    `,document.head.appendChild(t)}}function _(c,t){let e;return function(...i){const n=()=>{clearTimeout(e),c(...i)};clearTimeout(e),e=setTimeout(n,t)}}function z(c,t){let e,i,n;return function(...l){e?(clearTimeout(i),i=setTimeout(()=>{Date.now()-n>=t&&(c(...l),n=Date.now())},t-(Date.now()-n))):(c(...l),n=Date.now(),e=!0)}}class B{static handleMessage(t,e){["SETTINGS_UPDATED","SETTINGS_LOADED","SETTINGS_CHANGED"].includes(t.type)&&e({success:!0})}}class j{constructor(){f(this,"configManager");f(this,"urlMonitor");f(this,"settings");f(this,"throttledProcessProducts");f(this,"debouncedSaveSettings");this.configManager=new T,this.urlMonitor=new q(()=>this.handleUrlChange()),this.settings={},this.throttledProcessProducts=z(()=>{M.processProducts(this.settings,(t,e)=>{this.handleProductHover(t,e)})},200),this.debouncedSaveSettings=_(()=>{this.configManager.saveSettings(this.settings)},50)}async initialize(){try{await this.loadSettings(),this.setupMessageListener(),this.urlMonitor.startMonitoring(),this.setupMutationObserver(),R.addStyles(),setTimeout(()=>{E.createControlPanel(this.settings,t=>{this.updateSettings(t)}),this.throttledProcessProducts(),this.settings.alipriceCostFilterEnabled&&R.applyStyles(this.settings)},1e3),setInterval(()=>{this.ensureControlPanelPresent()},2e3)}catch(t){console.error("Failed to initialize Content Script:",t)}}async loadSettings(t=!1){this.settings=await this.configManager.loadSettings(t),t||(this.settings=this.configManager.validatePageChange(this.settings))}setupMessageListener(){chrome.runtime.onMessage.addListener((t,e,i)=>{B.handleMessage(t,i),["SETTINGS_UPDATED","SETTINGS_LOADED","SETTINGS_CHANGED"].includes(t.type)&&(this.updateSettingsFromMessage(t.settings),this.settings.alipriceCostFilterEnabled&&R.applyStyles(this.settings),this.throttledProcessProducts())})}setupMutationObserver(){new MutationObserver(_(()=>{this.throttledProcessProducts()},320)).observe(document.body,{childList:!0,subtree:!0}),new MutationObserver(_(()=>{this.settings.alipriceCostFilterEnabled&&R.applyStyles(this.settings)},500)).observe(document.body,{childList:!0,subtree:!0})}handleUrlChange(){this.settings.기준가격=20,this.settings.lastHoveredCost=null,this.settings.lastHoveredProductId=null,document.querySelectorAll(".recommended-cost").forEach(t=>t.remove()),document.querySelectorAll("[data-hover-registered]").forEach(t=>{t.removeAttribute("data-hover-registered")}),this.loadSettings(!0).then(()=>{this.settings.alipriceCostFilterEnabled&&R.applyStyles(this.settings),setTimeout(()=>{this.ensureControlPanelPresent(),this.throttledProcessProducts()},500)})}handleProductHover(t,e){this.settings.lastHoveredCost=t,this.settings.기준가격=t,this.settings.lastHoveredProductId=e,this.settings.alipriceCostFilterEnabled&&R.applyStyles(this.settings),this.debouncedSaveSettings()}updateSettings(t){this.settings=y(y({},this.settings),t),R.applyStyles(this.settings),this.debouncedSaveSettings(),this.throttledProcessProducts()}updateSettingsFromMessage(t){var e,i,n,l,m,p,o,r,a,s,g,u,h,x,d;Object.assign(this.settings,{coupangMarginRate:(e=t.coupangMarginRate)!=null?e:this.settings.coupangMarginRate,supplyMarginRate:(i=t.supplyMarginRate)!=null?i:this.settings.supplyMarginRate,exchangeRate:(n=t.exchangeRate)!=null?n:this.settings.exchangeRate,minRatingFilter:(l=t.minRatingFilter)!=null?l:this.settings.minRatingFilter,maxPriceFilter:(m=t.maxPriceFilter)!=null?m:this.settings.maxPriceFilter,minReviewsFilter:(p=t.minReviewsFilter)!=null?p:this.settings.minReviewsFilter,excludeRocketDelivery:(o=t.excludeRocketDelivery)!=null?o:this.settings.excludeRocketDelivery,isFilterEnabled:(r=t.isFilterEnabled)!=null?r:this.settings.isFilterEnabled,alipriceCostFilterEnabled:(a=t.alipriceCostFilterEnabled)!=null?a:this.settings.alipriceCostFilterEnabled,기준가격:(s=t.기준가격)!=null?s:this.settings.기준가격,lastHoveredCost:(g=t.lastHoveredCost)!=null?g:this.settings.lastHoveredCost,lastHoveredProductId:(u=t.lastHoveredProductId)!=null?u:this.settings.lastHoveredProductId,minRatingFilterEnabled:(h=t.minRatingFilterEnabled)!=null?h:this.settings.minRatingFilterEnabled,maxPriceFilterEnabled:(x=t.maxPriceFilterEnabled)!=null?x:this.settings.maxPriceFilterEnabled,minReviewsFilterEnabled:(d=t.minReviewsFilterEnabled)!=null?d:this.settings.minReviewsFilterEnabled})}ensureControlPanelPresent(){const t=document.getElementById("cost-filter-panel"),e=document.getElementById("cost-filter-toggle");(!t||!e)&&E.createControlPanel(this.settings,i=>{this.updateSettings(i)})}}const A=new j;document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>A.initialize()):A.initialize();function V(c){return c&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")?c.default:c}var I={exports:{}};(function(c){var t=Object.prototype.hasOwnProperty,e="~";function i(){}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(e=!1));function n(o,r,a){this.fn=o,this.context=r,this.once=a||!1}function l(o,r,a,s,g){if(typeof a!="function")throw new TypeError("The listener must be a function");var u=new n(a,s||o,g),h=e?e+r:r;return o._events[h]?o._events[h].fn?o._events[h]=[o._events[h],u]:o._events[h].push(u):(o._events[h]=u,o._eventsCount++),o}function m(o,r){--o._eventsCount===0?o._events=new i:delete o._events[r]}function p(){this._events=new i,this._eventsCount=0}p.prototype.eventNames=function(){var r=[],a,s;if(this._eventsCount===0)return r;for(s in a=this._events)t.call(a,s)&&r.push(e?s.slice(1):s);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(a)):r},p.prototype.listeners=function(r){var a=e?e+r:r,s=this._events[a];if(!s)return[];if(s.fn)return[s.fn];for(var g=0,u=s.length,h=new Array(u);g<u;g++)h[g]=s[g].fn;return h},p.prototype.listenerCount=function(r){var a=e?e+r:r,s=this._events[a];return s?s.fn?1:s.length:0},p.prototype.emit=function(r,a,s,g,u,h){var x=e?e+r:r;if(!this._events[x])return!1;var d=this._events[x],w=arguments.length,P,b;if(d.fn){switch(d.once&&this.removeListener(r,d.fn,void 0,!0),w){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,a),!0;case 3:return d.fn.call(d.context,a,s),!0;case 4:return d.fn.call(d.context,a,s,g),!0;case 5:return d.fn.call(d.context,a,s,g,u),!0;case 6:return d.fn.call(d.context,a,s,g,u,h),!0}for(b=1,P=new Array(w-1);b<w;b++)P[b-1]=arguments[b];d.fn.apply(d.context,P)}else{var O=d.length,C;for(b=0;b<O;b++)switch(d[b].once&&this.removeListener(r,d[b].fn,void 0,!0),w){case 1:d[b].fn.call(d[b].context);break;case 2:d[b].fn.call(d[b].context,a);break;case 3:d[b].fn.call(d[b].context,a,s);break;case 4:d[b].fn.call(d[b].context,a,s,g);break;default:if(!P)for(C=1,P=new Array(w-1);C<w;C++)P[C-1]=arguments[C];d[b].fn.apply(d[b].context,P)}}return!0},p.prototype.on=function(r,a,s){return l(this,r,a,s,!1)},p.prototype.once=function(r,a,s){return l(this,r,a,s,!0)},p.prototype.removeListener=function(r,a,s,g){var u=e?e+r:r;if(!this._events[u])return this;if(!a)return m(this,u),this;var h=this._events[u];if(h.fn)h.fn===a&&(!g||h.once)&&(!s||h.context===s)&&m(this,u);else{for(var x=0,d=[],w=h.length;x<w;x++)(h[x].fn!==a||g&&!h[x].once||s&&h[x].context!==s)&&d.push(h[x]);d.length?this._events[u]=d.length===1?d[0]:d:m(this,u)}return this},p.prototype.removeAllListeners=function(r){var a;return r?(a=e?e+r:r,this._events[a]&&m(this,a)):(this._events=new i,this._eventsCount=0),this},p.prototype.off=p.prototype.removeListener,p.prototype.addListener=p.prototype.on,p.prefixed=e,p.EventEmitter=p,c.exports=p})(I);var K=I.exports;const Y=V(K),W=async(c,t=2e4)=>new Promise((e,i)=>{var n;if(!((n=chrome==null?void 0:chrome.runtime)!=null&&n.id)){i(new Error("Extension context invalidated. Please refresh the page."));return}try{const l=setTimeout(()=>{i(new Error("Timeout"))},t);chrome.runtime.sendMessage(c,m=>{clearTimeout(l),chrome.runtime.lastError?i(chrome.runtime.lastError):m?e(m):i(new Error("No response from background script."))})}catch(l){console.error("Message sending error:",l),i(new Error("Cannot communicate with extension. Please refresh the page."))}}),J=c=>{window.postMessage(c,window.location.origin)},Q={"background-worker":["chrome-extension://*"],1688:["https://1688.com","https://www.1688.com","https://detail.1688.com"],coupang:["https://coupang.com"],"supplier-hub":["https://supplier.coupang.com"],app:Array.from(new Set(["http://localhost:4000",void 0])).filter(c=>c!==void 0),"app-bridge":Array.from(new Set(["http://localhost:4000",void 0])).filter(c=>c!==void 0)};class X extends Y{constructor(e){super();f(this,"iam");this.iam=e,this.registerMessageHandler()}registerMessageHandler(){window.addEventListener("message",e=>{var n;if(!Q[this.iam].includes(e.origin)||e.data.target!==this.iam)return;const{data:i}=e;this.emit(i.type,(n=i.data)!=null?n:{},l=>{this.send(i.sender,i.type,l,1e4,i.id)})}),chrome.runtime.onMessage.addListener((e,i,n)=>{const{type:l,data:m}=e;this.emit(l,m!=null?m:{},n)})}generateMessageId(){return crypto.randomUUID()}async send(e,i,n,l=2e4,m){const p=this.generateMessageId(),o={id:m!=null?m:p,type:i,data:n,sender:this.iam,target:e,timestamp:Date.now()};if(e==="app"){J(o);return}const r=await W(o,l);if((r==null?void 0:r.status)==="error")throw new Error(r.message);return"data"in r?r.data:void 0}}const Z=new X("coupang");Z.on("sync-currency-settings",c=>{});
