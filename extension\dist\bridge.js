var O=Object.defineProperty;var x=Object.getOwnPropertySymbols;var M=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable;var E=(t,r,e)=>r in t?O(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e,k=(t,r)=>{for(var e in r||(r={}))M.call(r,e)&&E(t,e,r[e]);if(x)for(var e of x(r))L.call(r,e)&&E(t,e,r[e]);return t};var C=(t,r,e)=>E(t,typeof r!="symbol"?r+"":r,e);function P(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var A={exports:{}};(function(t){var r=Object.prototype.hasOwnProperty,e="~";function i(){}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(e=!1));function d(c,n,o){this.fn=c,this.context=n,this.once=o||!1}function g(c,n,o,a,m){if(typeof o!="function")throw new TypeError("The listener must be a function");var f=new d(o,a||c,m),u=e?e+n:n;return c._events[u]?c._events[u].fn?c._events[u]=[c._events[u],f]:c._events[u].push(f):(c._events[u]=f,c._eventsCount++),c}function p(c,n){--c._eventsCount===0?c._events=new i:delete c._events[n]}function h(){this._events=new i,this._eventsCount=0}h.prototype.eventNames=function(){var n=[],o,a;if(this._eventsCount===0)return n;for(a in o=this._events)r.call(o,a)&&n.push(e?a.slice(1):a);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(o)):n},h.prototype.listeners=function(n){var o=e?e+n:n,a=this._events[o];if(!a)return[];if(a.fn)return[a.fn];for(var m=0,f=a.length,u=new Array(f);m<f;m++)u[m]=a[m].fn;return u},h.prototype.listenerCount=function(n){var o=e?e+n:n,a=this._events[o];return a?a.fn?1:a.length:0},h.prototype.emit=function(n,o,a,m,f,u){var v=e?e+n:n;if(!this._events[v])return!1;var s=this._events[v],y=arguments.length,_,l;if(s.fn){switch(s.once&&this.removeListener(n,s.fn,void 0,!0),y){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,o),!0;case 3:return s.fn.call(s.context,o,a),!0;case 4:return s.fn.call(s.context,o,a,m),!0;case 5:return s.fn.call(s.context,o,a,m,f),!0;case 6:return s.fn.call(s.context,o,a,m,f,u),!0}for(l=1,_=new Array(y-1);l<y;l++)_[l-1]=arguments[l];s.fn.apply(s.context,_)}else{var z=s.length,b;for(l=0;l<z;l++)switch(s[l].once&&this.removeListener(n,s[l].fn,void 0,!0),y){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,o);break;case 3:s[l].fn.call(s[l].context,o,a);break;case 4:s[l].fn.call(s[l].context,o,a,m);break;default:if(!_)for(b=1,_=new Array(y-1);b<y;b++)_[b-1]=arguments[b];s[l].fn.apply(s[l].context,_)}}return!0},h.prototype.on=function(n,o,a){return g(this,n,o,a,!1)},h.prototype.once=function(n,o,a){return g(this,n,o,a,!0)},h.prototype.removeListener=function(n,o,a,m){var f=e?e+n:n;if(!this._events[f])return this;if(!o)return p(this,f),this;var u=this._events[f];if(u.fn)u.fn===o&&(!m||u.once)&&(!a||u.context===a)&&p(this,f);else{for(var v=0,s=[],y=u.length;v<y;v++)(u[v].fn!==o||m&&!u[v].once||a&&u[v].context!==a)&&s.push(u[v]);s.length?this._events[f]=s.length===1?s[0]:s:p(this,f)}return this},h.prototype.removeAllListeners=function(n){var o;return n?(o=e?e+n:n,this._events[o]&&p(this,o)):(this._events=new i,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=e,h.EventEmitter=h,t.exports=h})(A);var S=A.exports;const T=P(S),q=async(t,r=2e4)=>new Promise((e,i)=>{var d;if(!((d=chrome==null?void 0:chrome.runtime)!=null&&d.id)){i(new Error("Extension context invalidated. Please refresh the page."));return}try{const g=setTimeout(()=>{i(new Error("Timeout"))},r);chrome.runtime.sendMessage(t,p=>{clearTimeout(g),chrome.runtime.lastError?i(chrome.runtime.lastError):p?e(p):i(new Error("No response from background script."))})}catch(g){console.error("Message sending error:",g),i(new Error("Cannot communicate with extension. Please refresh the page."))}}),I=t=>{window.postMessage(t,window.location.origin)},U={"background-worker":["chrome-extension://*"],1688:["https://1688.com","https://www.1688.com","https://detail.1688.com"],coupang:["https://coupang.com"],"supplier-hub":["https://supplier.coupang.com"],app:Array.from(new Set(["http://localhost:4000",void 0])).filter(t=>t!==void 0),"app-bridge":Array.from(new Set(["http://localhost:4000",void 0])).filter(t=>t!==void 0)};class N extends T{constructor(e){super();C(this,"iam");this.iam=e,this.registerMessageHandler()}registerMessageHandler(){window.addEventListener("message",e=>{var d;if(!U[this.iam].includes(e.origin)||e.data.target!==this.iam)return;const{data:i}=e;this.emit(i.type,(d=i.data)!=null?d:{},g=>{this.send(i.sender,i.type,g,1e4,i.id)})}),chrome.runtime.onMessage.addListener((e,i,d)=>{const{type:g,data:p}=e;this.emit(g,p!=null?p:{},d)})}generateMessageId(){return crypto.randomUUID()}async send(e,i,d,g=2e4,p){const h=this.generateMessageId(),c={id:p!=null?p:h,type:i,data:d,sender:this.iam,target:e,timestamp:Date.now()};if(e==="app"){I(c);return}const n=await q(c,g);if((n==null?void 0:n.status)==="error")throw new Error(n.message);return"data"in n?n.data:void 0}}const w=new N("app-bridge"),V=({codeChallenge:t,codeChallengeMethod:r="S256"})=>fetch("http://localhost:4000/api/auth/extension/authorization",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({code_challenge:t,code_challenge_method:r})}).then(async e=>{if(!e.ok)throw new Error("Failed to request authorization code");const i=await e.json();if(i.status==="error")throw new Error(i.message);return i.data});function D(t){let r="";for(let e=0;e<t.length;e++)r+=String.fromCharCode(t[e]);return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}function H(t=64){const r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",e=new Uint8Array(t);crypto.getRandomValues(e);let i="";for(let d=0;d<t;d++)i+=r[e[d]%r.length];return i}async function F(t){const r=new TextEncoder().encode(t),e=await crypto.subtle.digest("SHA-256",r);return new Uint8Array(e)}async function W(){const t=H(64),r=await F(t),e=D(r);return{codeVerifier:t,codeChallenge:e,codeChallengeMethod:"S256"}}w.on("request-upload-to-supplier-hub",async(t,r)=>{w.send("background-worker","request-upload-to-supplier-hub",t),r(void 0)});w.on("request-extension-auth",async t=>{const r=await w.send("background-worker","request-extension-auth",t);if(r.isError&&await w.send("app","update-authorization-status",{status:"error"}),r.logout){await w.send("app","update-authorization-status",{status:"unauthorized"});return}try{if(r.needAuth){await w.send("app","update-authorization-status",{status:"updating"});const{codeVerifier:e,codeChallenge:i,codeChallengeMethod:d}=await W(),g=await V({codeChallenge:i,codeChallengeMethod:d});await w.send("background-worker","send-authorization-code",k({codeVerifier:e},g))}await w.send("app","update-authorization-status",{status:"authorized"})}catch(e){await w.send("app","update-authorization-status",{status:"error",message:e instanceof Error?e.message:"Unknown error"})}});w.on("request-extension-metadata",async()=>{const t=await w.send("background-worker","check-is-authorized");w.send("app","broadcast-extension-metadata",{version:"3.4.1",authenticated:t})});
