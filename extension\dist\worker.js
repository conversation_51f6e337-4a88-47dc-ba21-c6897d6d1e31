var p=Object.defineProperty;var g=(a,e,t)=>e in a?p(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t;var i=(a,e,t)=>g(a,typeof e!="symbol"?e+"":e,t);class h extends Error{}h.prototype.name="InvalidTokenError";function m(a){return decodeURIComponent(atob(a).replace(/(.)/g,(e,t)=>{let r=t.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r}))}function f(a){let e=a.replace(/-/g,"+").replace(/_/g,"/");switch(e.length%4){case 0:break;case 2:e+="==";break;case 3:e+="=";break;default:throw new Error("base64 string is not of the correct length")}try{return m(e)}catch(t){return atob(e)}}function d(a,e){if(typeof a!="string")throw new h("Invalid token specified: must be a string");e||(e={});const t=e.header===!0?0:1,r=a.split(".")[t];if(typeof r!="string")throw new h(`Invalid token specified: missing part #${t+1}`);let s;try{s=f(r)}catch(n){throw new h(`Invalid token specified: invalid base64 for part #${t+1} (${n.message})`)}try{return JSON.parse(s)}catch(n){throw new h(`Invalid token specified: invalid json for part #${t+1} (${n.message})`)}}const u="http://localhost:4000_access_token",l="http://localhost:4000_refresh_token";class w{async checkNeedAuthCode(){const e=await this.getAccessToken(),t=await this.getRefreshToken();if(!e||!t)return"full";const r=d(e),s=d(t),n=Math.floor(Date.now()/1e3),o=r==null?void 0:r.exp,c=s==null?void 0:s.exp;return typeof c=="number"&&c<=n?"full":typeof o=="number"&&o<=n?"partial":"none"}getAccessToken(){return chrome.storage.local.get(u).then(e=>e[u])}setAccessToken(e){return chrome.storage.local.set({[u]:e})}getRefreshToken(){return chrome.storage.local.get(l).then(e=>e[l])}setRefreshToken(e){return chrome.storage.local.set({[l]:e})}removeTokens(){return chrome.storage.local.remove([u,l])}}const k=async(a,e)=>await fetch("http://localhost:4000/api/auth/extension/token",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({grant_type:"authorization_code",code:a,code_verifier:e})}).then(async t=>{if(!t.ok)throw new Error("Failed to fetch access token");const r=await t.json();if(r.status==="error")throw new Error(r.code);return r}),y=async a=>await fetch("http://localhost:4000/api/auth/extension/token",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({grant_type:"refresh_token",refresh_token:a})}).then(async e=>{if(!e.ok)throw new Error("Failed to fetch access token");const t=await e.json();if(t.status==="error")throw new Error(t.code);return t}),T="http://localhost:4000";class b{constructor(){i(this,"auth",new w)}async checkAuthAndRefresh(){const e=await this.auth.checkNeedAuthCode();if(e==="full")return{needAuth:!0};if(e==="partial"){try{await this.refreshAccessToken()}catch(t){throw this.auth.removeTokens(),t}return{needAuth:!1,isRefreshing:!0}}return{needAuth:!1}}async checkAuth(){const e=await this.auth.getAccessToken(),t=await this.auth.getRefreshToken();return!(!e||!t)}async logout(){await this.auth.removeTokens()}async refreshAccessToken(){const e=await this.auth.getRefreshToken();if(!e)throw new Error("Refresh token not found");const{access_token:t,refresh_token:r}=await y(e);await this.auth.setAccessToken(t),await this.auth.setRefreshToken(r)}async sendAuthorizationCode({codeVerifier:e,code:t}){const{access_token:r,refresh_token:s}=await k(t,e);return await this.auth.setAccessToken(r),await this.auth.setRefreshToken(s),!0}async sendCrawlData(e){var s;if(!e||!e.product.id)throw new Error("데이터가 없습니다.");const t=await this.auth.getAccessToken();if(!t)throw new Error("인증 정보가 없습니다. 사이트에 접속하여 로그인한 후 다시 수집해주세요.");const r=await fetch(`${T}/api/products`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Extension-Bearer ${t}`},credentials:"include",body:JSON.stringify(e)});if(!r.ok){if((s=r.headers.get("content-type"))!=null&&s.includes("application/json")){const n=await r.json();throw new Error(n.message||"크롤링 데이터 전송 실패")}throw new Error(`크롤링 데이터 전송 실패: ${r.status}`)}return await r.json()}}class v{constructor(){this.setupStorageListener()}setupStorageListener(){chrome.storage.onChanged.addListener((e,t)=>{t==="local"&&e.coupangCostCalculator&&this.broadcastSettings(e.coupangCostCalculator.newValue)})}async getSettings(){return(await chrome.storage.local.get(["coupangCostCalculator"])).coupangCostCalculator||this.getDefaultSettings()}async saveSettings(e){await chrome.storage.local.set({coupangCostCalculator:e})}async updateBasePrice(e,t){const r=await this.getSettings();r.기준가격=e,r.lastHoveredCost=e,r.lastHoveredProductId=t||null,await this.saveSettings(r)}async sendSettingsToTab(e){setTimeout(async()=>{const t=await this.getSettings();chrome.tabs.sendMessage(e,{type:"SETTINGS_LOADED",settings:t}).catch(()=>{})},1e3)}broadcastSettings(e){chrome.tabs.query({url:"*://*.coupang.com/*"},t=>{t.forEach(r=>{r.id&&chrome.tabs.sendMessage(r.id,{type:"SETTINGS_CHANGED",settings:e}).catch(()=>{})})})}getDefaultSettings(){return{coupangMarginRate:.4,supplyMarginRate:.4,exchangeRate:320,minRatingFilter:4,maxPriceFilter:1e5,minReviewsFilter:50,excludeRocketDelivery:!1,isFilterEnabled:!0,alipriceCostFilterEnabled:!0,기준가격:20,lastHoveredCost:null,lastHoveredProductId:null}}async findSupplierTab(){return new Promise(e=>{chrome.tabs.query({url:"*://supplier.coupang.com/*"},t=>{e(t.length>0?t[0]:null)})})}async openSupplierTab(){return new Promise(e=>{chrome.tabs.create({url:"https://supplier.coupang.com/sr/registration/step/startPage",active:!0},t=>{e(t)})})}async checkTab(){const e=await this.findSupplierTab();return{hasTab:!!e,tabId:e==null?void 0:e.id}}notifyUploadProgress(e,t,r){chrome.tabs.query({url:"*://localhost:3000/*"},s=>{s.forEach(n=>{n.id&&chrome.tabs.sendMessage(n.id,{action:"COUPANG_UPLOAD_PROGRESS",step:e,message:t,coupangTabId:r}).catch(()=>{})})})}async activateTab(e){return new Promise(t=>{chrome.tabs.update(e,{active:!0},()=>t())})}async waitForTabLoad(e){return new Promise(t=>{const r=(s,n)=>{s===e&&n.status==="complete"&&(chrome.tabs.onUpdated.removeListener(r),t())};chrome.tabs.onUpdated.addListener(r)})}async sendMessageToTab(e,t){return new Promise((r,s)=>{chrome.tabs.sendMessage(e,t,n=>{chrome.runtime.lastError?s(new Error(chrome.runtime.lastError.message)):r(n)})})}}const S=a=>new Promise(e=>setTimeout(e,a)),E=(a,e,t)=>chrome.tabs.sendMessage(a,{type:e,data:t}),C=a=>new Promise((e,t)=>{chrome.tabs.get(a,r=>{r.status==="complete"&&e(r)}),chrome.tabs.onUpdated.addListener((r,s,n)=>{r===r&&s.status==="complete"&&e(n)})}),A=(a,e)=>new Promise((t,r)=>{const s=(o,c)=>{o===a&&c.url&&c.url.includes(e)&&(chrome.tabs.onUpdated.removeListener(s),chrome.tabs.onRemoved.removeListener(n),t())},n=o=>{o===a&&(chrome.tabs.onUpdated.removeListener(s),chrome.tabs.onRemoved.removeListener(n),r(new Error("탭이 닫혔습니다")))};chrome.tabs.onUpdated.addListener(s),chrome.tabs.onRemoved.addListener(n)}),R=async()=>chrome.tabs.create({url:"https://supplier.coupang.com/sr/registration/step/startPage"}),_=async a=>{await new Promise((e,t)=>{const r=setInterval(async()=>{try{const s=await chrome.tabs.get(a);s.url&&s.url.includes("xauth.coupang.com")&&(clearInterval(r),e())}catch(s){clearInterval(r),t(new Error("탭이 닫혔습니다"))}},500)}),await A(a,"https://supplier.coupang.com")};class L{constructor(){}async upload(e){const t=await R();await C(t.id),await S(500),t.url&&t.url.includes("xauth.coupang.com")&&await _(t.id),await E(t.id,"upload-product-to-coupang-supplier",e)}}class P{constructor(){i(this,"app");i(this,"coupang");i(this,"supplier");this.app=new b,this.coupang=new v,this.supplier=new L,this.init()}init(){chrome.runtime.onMessage.addListener((e,t,r)=>(this.handleMessage(e,t).then(s=>{r({type:e.type,id:e.id,timestamp:e.timestamp,sender:e.target,target:e.sender,data:s!=null?s:void 0})}).catch(s=>{r({type:e.type,id:e.id,timestamp:e.timestamp,sender:e.target,target:e.sender,data:void 0,status:"error",message:s instanceof Error?s.message:"Unknown error"})}),!0)),chrome.runtime.onInstalled.addListener(e=>{e.reason==="install"&&this.setDefaultSettings()}),chrome.tabs.onUpdated.addListener((e,t,r)=>{t.status==="complete"&&r.url&&r.url.includes("coupang.com")&&this.coupang.sendSettingsToTab(e)})}async handleMessage(e,t){if(e.target==="background-worker")switch(e.type){case"send-crawl-data":return this.app.sendCrawlData(e.data);case"request-extension-auth":return await(async()=>{var r;return(r=e.data)!=null&&r.logout?(await this.app.logout(),{needAuth:!0,isError:!1,isRefreshed:!1,logout:!0}):this.app.checkAuthAndRefresh()})();case"send-authorization-code":return this.app.sendAuthorizationCode(e.data);case"check-is-authorized":return this.app.checkAuth();case"request-upload-to-supplier-hub":return this.supplier.upload(e.data)}}setDefaultSettings(){const e={coupangMarginRate:.4,supplyMarginRate:.4,exchangeRate:320,minRatingFilter:4,maxPriceFilter:1e5,minReviewsFilter:50,excludeRocketDelivery:!1,isFilterEnabled:!0,alipriceCostFilterEnabled:!0,기준가격:20,lastHoveredCost:null,lastHoveredProductId:null};chrome.storage.local.set({coupangCostCalculator:e})}}new P;
